<template>
  <div class="min-h-screen p-8">
    <h1 class="text-2xl font-bold mb-4">GitHub免登录功能测试</h1>
    
    <div class="mb-4">
      <input 
        v-model="testUsername" 
        type="text" 
        placeholder="输入GitHub用户名" 
        class="border p-2 mr-2"
      />
      <button 
        @click="testCachedResult" 
        class="bg-blue-500 text-white px-4 py-2 rounded"
        :disabled="loading"
      >
        测试缓存结果
      </button>
    </div>

    <div v-if="loading" class="text-blue-500">
      正在测试...
    </div>

    <div v-if="result" class="mt-4">
      <h2 class="text-lg font-semibold mb-2">测试结果:</h2>
      <pre class="bg-gray-100 p-4 rounded">{{ JSON.stringify(result, null, 2) }}</pre>
    </div>

    <div v-if="error" class="mt-4 text-red-500">
      <h2 class="text-lg font-semibold mb-2">错误信息:</h2>
      <p>{{ error }}</p>
    </div>

    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-2">预设测试用户:</h2>
      <div class="space-x-2">
        <button 
          @click="testUser('torvalds')" 
          class="bg-green-500 text-white px-3 py-1 rounded"
        >
          torvalds (有缓存)
        </button>
        <button 
          @click="testUser('testuser123')" 
          class="bg-red-500 text-white px-3 py-1 rounded"
        >
          testuser123 (无缓存)
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getWithoutAuth } from '~/utils/request'

const testUsername = ref('torvalds')
const loading = ref(false)
const result = ref(null)
const error = ref('')

const testCachedResult = async () => {
  if (!testUsername.value.trim()) return
  
  loading.value = true
  result.value = null
  error.value = ''
  
  try {
    console.log('🧪 Testing cached result for:', testUsername.value)
    const response = await getWithoutAuth('/api/github/get_result', { 
      user1: testUsername.value.trim() 
    })
    
    console.log('✅ Success:', response)
    result.value = response
  } catch (err: any) {
    console.log('❌ Error:', err.message)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

const testUser = (username: string) => {
  testUsername.value = username
  testCachedResult()
}
</script>
