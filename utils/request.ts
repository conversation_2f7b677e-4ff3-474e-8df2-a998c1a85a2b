interface ResponseData<T = any> {
  code: number
  message: string
  data: T
}


export const getApiBaseUrl = () => {
  try {
    const runtimeConfig = useRuntimeConfig()
    return runtimeConfig.public.apiBase || 'https://api.dinq.io'
  } catch (error) {
    console.warn('Could not access runtime config, using default API base URL')
    return 'https://api.dinq.io'
  }
}

// 创建请求实例
const request = $fetch.create({
  // 使用相对路径，让Nuxt处理代理
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  onRequest({ options, request }) {
    // 添加固定的userid头部用于API认证绕过
    const headers = options.headers as any
    
    // 从headers中获取Userid
    // if (headers.Userid) {
    //   headers.userid = headers.Userid
    //   delete headers.Userid
    // }
    console.log('headers', request, options)

    try {
      // 获取运行时配置
      const runtimeConfig = useRuntimeConfig()

      // 如果在预渲染模式，则不发送实际请求
      if (runtimeConfig.public.isPrerenderMode) {
        console.log('Mocking API request during prerendering')
        // 保留原始请求体，只添加模拟标志
        const originalBody = options.body ? JSON.parse(options.body as string) : {}
        options.body = JSON.stringify({ ...originalBody, _mocked: true })
      }

      // 如果有自定义域名，则修改请求URL
      if (typeof window !== 'undefined' && runtimeConfig.public.apiBase) {
        const apiBaseUrl = runtimeConfig.public.apiBase
        const requestUrl = request.toString()
        if (requestUrl.startsWith('/api')) {
          options.baseURL = apiBaseUrl
          console.log(`Using API base URL: ${apiBaseUrl} for request: ${requestUrl}`)
        }
      }
    } catch (error) {
      console.warn('Could not access runtime config, using default API path')
    }
  },
  onResponse({ response }) {
    // 检查是否是模拟响应
    const data = response._data
    if (data && data._mocked) {
      // 返回一个模拟的成功响应
      return { success: true, data: {}, message: 'Mocked response during prerendering' }
    }
    return data
  },
  onResponseError({ response }) {
    console.error('Request Error:', response.status, response._data)
    throw new Error(response._data?.message || 'Request failed')
  },
})

export function get<T = any>(url: string, params?: Record<string, any>) {
  return request<ResponseData<T>>(url, { method: 'GET', params })
}




export function getFunc<T = any>(url: string, params?: Record<string, any>,  headers?: Record<string, string>) {

  // 构建查询参数
  const queryParams = new URLSearchParams()
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString())
      }
    })
  }

  // 构建完整URL
  let fullUrl = url
  if (url.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${url}`
  }

  // 添加查询参数
  const queryString = queryParams.toString()
  if (queryString) {
    fullUrl += `?${queryString}`
  }

  const requestHeaders = {
    ...headers,
    // ...(headers?.Userid ? { userid: headers.Userid } : {})
  }

  // 使用原生fetch发送请求
  return fetch(fullUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...requestHeaders
    }
  }).then(async (response) => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data as ResponseData<T>
  })
}


export async function post<T = any>(
  url: string, 
  body?: Record<string, any>, 
  headers?: Record<string, string>
): Promise<ResponseData<T>> {
  // 处理 Userid 头
  const requestHeaders = {
    ...headers,
    // ...(headers?.Userid ? { userid: headers.Userid } : {})
  }
  
  const response = await request<T>(url, {
    method: 'POST',
    headers: requestHeaders,
    body: body ? JSON.stringify(body) : undefined
  })

  return {
    code: 200,
    data: response as T,
    message: 'success'
  }
}


export async function postFile<T = any>(url: string, body?: FormData, headers?: Record<string, any>) {
  
    // 构建查询参数
    // const queryParams = new URLSearchParams()
    // if (params) {
    //   Object.entries(params).forEach(([key, value]) => {
    //     if (value !== undefined && value !== null && value !== '') {
    //       queryParams.append(key, value.toString())
    //     }
    //   })
    // }

    // 构建完整URL
    let fullUrl = url
    if (url.startsWith('/')) {
      const apiBaseUrl = getApiBaseUrl()
      fullUrl = `${apiBaseUrl}${url}`
    }
  
    // 添加查询参数
    // const queryString = queryParams.toString()
    // if (queryString) {
    //   fullUrl += `?${queryString}`
    // }
  // 不要手动设置 Content-Type，fetch 会自动处理
  const response = await fetch(fullUrl, {
    method: 'POST',
    headers: headers,
    body: body
  })
  const data = await response.json()
  return {
    code: data.code ?? response.status,
    message: data.message ?? (response.ok ? 'success' : 'error'),
    data: data.data as T
  } as ResponseData<T>
}

export function del<T = any>(url: string, headers?: Record<string, any>) {
  // 如果headers中包含Userid，确保它被正确传递
  const requestHeaders = {
    ...headers,
    // ...(headers?.Userid ? { userid: headers.Userid } : {})
  }

  let fullUrl = url

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (url.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${url}`
  }

  return fetch(fullUrl, {
    method: 'DELETE',
    headers: requestHeaders,
    // credentials: 'include' // 确保发送跨域cookie
  }).then(async (response) => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data as ResponseData<T>
  })
}

export function put<T = any>(url: string, body?: Record<string, any>, headers?: Record<string, any>) {
  // 如果headers中包含Userid，确保它被正确传递
  const requestHeaders = {
    ...headers,
    'Content-Type': 'application/json'
  }

  let fullUrl = url

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (url.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${url}`
  }

  return fetch(fullUrl, {
    method: 'PUT',
    headers: requestHeaders,
    body: body ? JSON.stringify(body) : undefined,
    // credentials: 'include' // 确保发送跨域cookie
  }).then(async (response) => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    return data as ResponseData<T>
  })
}

// 新增：免鉴权GET请求函数，专门用于获取缓存数据
export function getWithoutAuth<T = any>(url: string, params?: Record<string, any>): Promise<ResponseData<T>> {
  let fullUrl = url

  // 如果是相对路径，添加域名
  if (url.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${url}`
  }

  // 添加查询参数
  if (params) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    const queryString = searchParams.toString()
    if (queryString) {
      fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString
    }
  }

  return fetch(fullUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      // 不包含任何认证头
    }
  }).then(async (response) => {
    const data = await response.json()

    if (!response.ok) {
      // 对于免鉴权接口，500状态码表示缓存未命中，这是正常情况
      if (response.status === 500) {
        throw new Error(`No cached data available`)
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return data as ResponseData<T>
  })
}

export default { get, post, del, put, getFunc, postFile, getWithoutAuth }
