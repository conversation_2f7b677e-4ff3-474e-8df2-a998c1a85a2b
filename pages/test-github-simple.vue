<template>
  <div class="min-h-screen p-8">
    <h1 class="text-2xl font-bold mb-4">GitHub免登录功能完整测试</h1>
    
    <div class="mb-4">
      <input 
        v-model="testUsername" 
        type="text" 
        placeholder="输入GitHub用户名" 
        class="border p-2 mr-2"
      />
      <button 
        @click="testCompleteFlow" 
        class="bg-blue-500 text-white px-4 py-2 rounded mr-2"
        :disabled="loading"
      >
        测试完整流程
      </button>
      <button 
        @click="resetTest" 
        class="bg-gray-500 text-white px-4 py-2 rounded"
      >
        重置
      </button>
    </div>

    <div v-if="loading" class="text-blue-500 mb-4">
      {{ thinking }}
    </div>

    <div v-if="result" class="mt-4">
      <h2 class="text-lg font-semibold mb-2 text-green-600">✅ 成功获取数据:</h2>
      <div class="bg-green-50 p-4 rounded border">
        <p><strong>用户名:</strong> {{ result.name }}</p>
        <p><strong>头像:</strong> <img :src="result.user?.avatar_url" class="w-8 h-8 inline-block ml-2" /></p>
        <p><strong>简介:</strong> {{ result.user?.bio || '无' }}</p>
        <p><strong>活跃天数:</strong> {{ result.overview?.active_days || 'N/A' }}</p>
      </div>
    </div>

    <div v-if="error" class="mt-4">
      <h2 class="text-lg font-semibold mb-2 text-red-600">❌ 错误信息:</h2>
      <div class="bg-red-50 p-4 rounded border">
        <p>{{ error }}</p>
      </div>
    </div>

    <div v-if="showLoginPrompt" class="mt-4">
      <h2 class="text-lg font-semibold mb-2 text-yellow-600">🔐 需要登录:</h2>
      <div class="bg-yellow-50 p-4 rounded border">
        <p>缓存中没有该用户的数据，需要登录后进行分析。</p>
        <button class="bg-blue-500 text-white px-4 py-2 rounded mt-2">
          登录
        </button>
      </div>
    </div>

    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-2">预设测试用户:</h2>
      <div class="space-x-2">
        <button 
          @click="testUser('torvalds')" 
          class="bg-green-500 text-white px-3 py-1 rounded"
        >
          torvalds (有缓存)
        </button>
        <button 
          @click="testUser('testuser123')" 
          class="bg-red-500 text-white px-3 py-1 rounded"
        >
          testuser123 (无缓存)
        </button>
        <button 
          @click="testUser('octocat')" 
          class="bg-green-500 text-white px-3 py-1 rounded"
        >
          octocat (有缓存)
        </button>
      </div>
    </div>

    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-2">测试日志:</h2>
      <div class="bg-gray-100 p-4 rounded max-h-64 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="text-sm mb-1">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getWithoutAuth } from '~/utils/request'

const testUsername = ref('torvalds')
const loading = ref(false)
const thinking = ref('')
const result = ref(null)
const error = ref('')
const showLoginPrompt = ref(false)
const logs = ref<string[]>([])

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
}

const resetTest = () => {
  result.value = null
  error.value = ''
  showLoginPrompt.value = false
  thinking.value = ''
  loading.value = false
  logs.value = []
}

// 模拟完整的免登录分析流程
const testCompleteFlow = async () => {
  if (!testUsername.value.trim()) return
  
  resetTest()
  loading.value = true
  thinking.value = `正在检查 ${testUsername.value} 的缓存数据...`
  
  addLog(`🚀 开始分析用户: ${testUsername.value}`)
  addLog(`🔍 步骤1: 尝试获取缓存数据`)
  
  try {
    // 步骤1：尝试免鉴权接口获取缓存数据
    try {
      thinking.value = `正在查询缓存...`
      const response = await getWithoutAuth('/api/github/get_result', { 
        user1: testUsername.value.trim() 
      })
      
      addLog(`✅ 缓存命中! 找到用户数据`)
      result.value = response.data
      thinking.value = ''
      
    } catch (cacheError: any) {
      addLog(`❌ 缓存未命中: ${cacheError.message}`)
      
      // 步骤2：缓存未命中，检查登录状态
      addLog(`🔍 步骤2: 检查用户登录状态`)
      
      // 这里模拟检查登录状态（在实际应用中会检查 currentUser）
      const isLoggedIn = false // 模拟未登录状态
      
      if (!isLoggedIn) {
        addLog(`🔐 用户未登录，显示登录提示`)
        thinking.value = `分析需要登录。请登录后分析 ${testUsername.value}。`
        showLoginPrompt.value = true
      } else {
        addLog(`✅ 用户已登录，执行鉴权分析`)
        thinking.value = `正在分析 ${testUsername.value}...`
        // 这里会调用鉴权的分析接口
        // 为了演示，我们模拟一个成功的响应
        setTimeout(() => {
          result.value = {
            name: testUsername.value,
            user: {
              avatar_url: `https://github.com/${testUsername.value}.png`,
              bio: `通过鉴权分析获取的 ${testUsername.value} 数据`
            },
            overview: {
              active_days: 365
            }
          }
          thinking.value = ''
          addLog(`✅ 鉴权分析完成`)
        }, 2000)
      }
    }
  } catch (err: any) {
    addLog(`💥 发生错误: ${err.message}`)
    error.value = err.message
  } finally {
    loading.value = false
    if (!result.value && !showLoginPrompt.value && !error.value) {
      thinking.value = ''
    }
  }
}

const testUser = (username: string) => {
  testUsername.value = username
  testCompleteFlow()
}
</script>
