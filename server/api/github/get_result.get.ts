export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const user = query.user as string

    if (!user) {
      throw createError({
        statusCode: 400,
        statusMessage: 'user parameter is required'
      })
    }

    console.log(`Checking cached result for user: ${user}`)

    // 模拟缓存检查逻辑
    // 在实际实现中，这里应该查询数据库或缓存系统

    // 为了测试，我们模拟一些用户有缓存数据
    const usersWithCache = ['torvalds', 'octocat', 'github', 'magmueller']

    if (usersWithCache.includes(user.toLowerCase())) {
      // 直接返回缓存的分析数据（不包装）
      const mockData = {
        name: user,
        user: {
          avatar_url: `https://github.com/${user}.png`,
          bio: `Mock bio for ${user}`,
          tags: ['Developer', 'Open Source']
        },
        overview: {
          active_days: 365,
          pull_requests: 150,
          commits: 1200
        },
        code_contribution: {
          total: 50000
        },
        top_projects: [
          {
            repository: {
              name: 'mock-project',
              stargazerCount: 1000
            }
          }
        ]
      }

      console.log('Returning cached data directly:', mockData)
      return mockData
    } else {
      // 模拟缓存未命中，返回500状态码
      throw createError({
        statusCode: 500,
        statusMessage: 'No cached data available'
      })
    }
  } catch (error) {
    console.error('Error getting cached result:', error)
    
    // 如果是我们主动抛出的错误，直接传递
    if (error.statusCode) {
      throw error
    }
    
    // 其他错误统一返回500
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get cached result'
    })
  }
})
